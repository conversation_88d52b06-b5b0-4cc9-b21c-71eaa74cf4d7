// Mobile menu toggle
const menuToggle = document.querySelector('.menu__toggle');
const body = document.querySelector('body');

menuToggle.addEventListener('click', function () {
  body.classList.toggle('mobile-menu-open');
});

// Fix header position for admin bar
if (body.classList.contains('admin-bar')) {
  window.addEventListener('scroll', function () {
    if (window.scrollY > 46) {
      body.classList.add('scrolled-under-admin-bar');
    } else {
      body.classList.remove('scrolled-under-admin-bar');
    }
  });
}

// Accordion functionality
document.addEventListener('DOMContentLoaded', function () {
  const accordionItems = document.querySelectorAll('.accordion-item');

  // Add click event listeners to all accordion headers
  accordionItems.forEach(item => {
    const header = item.querySelector('.accordion-header');

    header.addEventListener('click', function () {
      // Toggle active class on the clicked item
      item.classList.toggle('active');
    });
  });
});

// Use Swiper without ES module imports
const swiper = new Swiper('.swiper-container', {
  loop: true,
  autoplay: {
    delay: 3000,
    disableOnInteraction: false,
  },
  navigation: false,
  allowTouchMove: false,
  effect: 'fade',
  fadeEffect: {
    crossFade: true,
  }
});

// Popup functionality
document.addEventListener('DOMContentLoaded', function () {
  // Handle links with href containing #popup_id
  document.addEventListener('click', function (e) {
    const link = e.target.closest('a[href*="#"]');
    if (link) {
      const href = link.getAttribute('href');
      const hashIndex = href.indexOf('#');
      if (hashIndex !== -1) {
        const popupId = href.substring(hashIndex + 1);

        // Check if this is a Calendly popup link
        if (typeof survilla_vars !== 'undefined' &&
            survilla_vars.calendly_id &&
            survilla_vars.calendly_url &&
            popupId === survilla_vars.calendly_id) {
          e.preventDefault();
          // Use Calendly popup widget instead of custom popup
          if (typeof Calendly !== 'undefined') {
            Calendly.initPopupWidget({url: survilla_vars.calendly_url});
          }
          return;
        }

        // Handle regular popups
        const popup = document.getElementById(popupId);
        const body = document.querySelector('body');
        if (popup && popup.classList.contains('popup')) {
          e.preventDefault();
          popup.classList.add('show');
          body.classList.add('popup-open');
        }
      }
    }
  });

  // Handle close button clicks
  document.addEventListener('click', function (e) {
    if (e.target.closest('.popup__close')) {
      const popup = e.target.closest('.popup');
      const body = document.querySelector('body');
      if (popup) {
        popup.classList.remove('show');
        body.classList.remove('popup-open');
      }
    }
  });

  // Handle clicks outside popup inner content
  document.addEventListener('click', function (e) {
    if (e.target.classList.contains('popup') && e.target.classList.contains('show')) {
      // Clicked directly on the popup overlay (outside popup__inner)
      e.target.classList.remove('show');
      document.querySelector('body').classList.remove('popup-open');
    }
  });
});