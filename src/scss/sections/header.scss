@use "../abstracts/variables" as variables;

// Header layout styles
.header {
    background-color: variables.$dark-blue;
    color: variables.$inverted-text-color;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 1px solid rgba(variables.$grey, 0.15);

    .row {
        padding-top: 7px;
        padding-bottom: 8px;

        @media screen and (max-width: variables.$breakpoint-md-max) {
            padding-top: 7px;
            padding-bottom: 7px;
            padding-left: 8px;
            padding-right: 8px;
        }
    }
    
    .header__logo {
        color: variables.$inverted-text-color;
        text-decoration: none;
        flex-shrink: 0;
    }

    .header__nav {
        margin-left: auto;
        
        @media screen and (max-width: variables.$breakpoint-sm-max) {
            display: none;
        }
    }

    .header__menu {
        display: flex;
        align-items: center;
        list-style-type: none;
        padding: 0;
        margin: 0;
        gap: 10px;

        .menu-item {
            list-style-type: none;
            line-height: 1;
            margin: 0;
            font-family: variables.$font-heading;

            a {
                display: block;
                line-height: 1;
                padding: 11.5px 15px;
                color: variables.$inverted-text-color;
                text-decoration: none;
                transition: color 0.3s ease-in-out, 
                            background-color 0.3s ease-in-out;

                &:hover {
                    color: variables.$light-blue;
                }
            }

            &:last-child {
                margin-right: 0;
            }
        }

        .menu-item__cta {
            a {
                font-size: 18px;
                line-height: 1;
                color: variables.$inverted-text-color;
                background-color: variables.$light-blue;
                padding: 10.5px 15px;
                border-radius: 15px;
                text-transform: uppercase;
                text-align: center;
            
                &:hover {
                    background-color: variables.$inverted-text-color;
                    color: variables.$light-blue;
                }
            }
        }
    }

    .menu__toggle {
        display: none;
        appearance: none;
        border: none;
        background: none;
        cursor: pointer;
        font-size: 20px;
        width: 48px;
        height: 39px;
        color: variables.$inverted-text-color;
        margin-left: auto;
        z-index: 1001;
        
        &::before {
            content: '\f0c9';
            font-family: "Font Awesome 6 Pro";
            -moz-osx-font-smoothing: grayscale;
            -webkit-font-smoothing: antialiased;
            display: inline-block;
            font-style: normal;
            font-variant: normal;
            line-height: 1;
            text-rendering: auto;
        }

        @media screen and (max-width: variables.$breakpoint-sm-max) {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

// Mobile menu styles
.mobile-menu-open {
    @media screen and (max-width: variables.$breakpoint-sm-max) {
        .menu__toggle {
            &::before {
                content: '\f00d';
            }
        }

        .header {
            .header__nav {
                display: block;
                position: fixed;
                top: 100px;
                left: 0;
                width: 100%;
                background-color: variables.$dark-blue;
                z-index: 1000;
                padding: 20px;
                border-bottom: 1px solid rgba(variables.$grey, 0.15);
            }
            
            .header__menu {
                flex-direction: column;
                align-items: stretch;
                
                .menu-item a {
                    padding: 10px 15px;
                    font-size: 16px;
                }
            }
        }
    }
}

// Admin bar styles
.admin-bar {
    .header {
        padding-top: 32px;

        @media screen and (max-width: 782px) {
            padding-top: 46px;
        }

        @media screen and (max-width: 600px) {
            position: absolute;
        }
    }

    &.mobile-menu-open {
        .header {
            .header__nav {
                @media screen and (max-width: 600px) {
                    position: absolute;
                }
            }
        }
    }
}

.admin-bar.scrolled-under-admin-bar {
    .header {
        @media screen and (max-width: 600px) {
            padding-top: 0;
            position: fixed;
        }
    }

    &.mobile-menu-open {
        .header {
            .header__nav {
                @media screen and (max-width: 600px) {
                    top: 54px;
                    position: fixed;
                }
            }
        }
    }
}

.spacer {
    display: block;
    height: 54px;
    width: 100%;
    background-color: variables.$dark-blue;
}