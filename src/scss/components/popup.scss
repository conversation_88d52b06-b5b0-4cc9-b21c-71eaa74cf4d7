@use '../abstracts/variables' as variables;

// Regular popup styles
body.popup-open {
    overflow: hidden;
}

.popup {
    opacity: 0;
    visibility: hidden;
    background-color: rgba(0, 0, 0, .7);
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 9999;

    &.show {
        opacity: 1;
        visibility: visible;
    }
}

.admin-bar .popup {
    top: 32px;

    @media screen and (max-width: 782px) {
        top: 46px;
    }
}

.admin-bar.scrolled-under-admin-bar .popup {
    @media screen and (max-width: 600px) {
        top: 0;
    }
}

.popup__inner {
    background-color: #fff;
    max-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
    max-width: calc(100% - 30px);

    @media screen and (max-width: 1029px) {
        width: 100%;
        max-width: 100%;
    }

    @media screen and (max-width: 649px) {
        border-radius: 0;
    }
}

.popup__content {
    position: relative;
    max-height: 100vh;
    min-width: 320px;
    display: flex;
    justify-content: center;
    align-items: center;

    @media screen and (max-width: 1029px) {
        width: 100%;
        max-width: 100%;
    }

}

.popup__close {
    appearance: none;
    background: none;
    border: none;
    cursor: pointer;
    position: absolute;
    right: 25px;
    top: 25px;
    width: 19px;
    height: 19px;
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
        content: '\f00d';
        font-family: "Font Awesome 6 Pro";
        -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
        color: variables.$inverted-text-color;
        display: inline-block;
        font-style: normal;
        font-variant: normal;
        line-height: 1;
        text-rendering: auto;
        font-size: 28px;
        transition: color 0.3s ease-in-out;

        @media screen and (max-width: 649px) {
            font-size: 26px;
        }
    }
}

// Calendly popup styles
body .calendly-overlay {
    background-color: rgba(0, 0, 0, .7);
}

body .calendly-overlay .calendly-popup {
    max-height: none!important;
}

.admin-bar .calendly-overlay {
    top: 32px;

    @media screen and (max-width: 782px) {
        top: 46px;
    }
}

.admin-bar.scrolled-under-admin-bar .calendly-overlay {
    @media screen and (max-width: 600px) {
        top: 0;
    }
}

.admin-bar .calendly-overlay .calendly-popup {
    @media (max-width: 975px) {
        top: calc(50px + 32px);
    }

    @media screen and (max-width: 782px) {
        top: calc(50px + 46px);
    }
}

.admin-bar.scrolled-under-admin-bar .calendly-overlay .calendly-popup {
    @media screen and (max-width: 600px) {
        top: 50px;
    }
}

.calendly-spinner {
    display: none!important;
}